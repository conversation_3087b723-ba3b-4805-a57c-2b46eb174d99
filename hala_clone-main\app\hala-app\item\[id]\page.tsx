"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { PageTransition } from "@/components/page-transition"
import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Loader2, ArrowLeft, Calendar, Tag, Info, User } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { useAuth } from "@/lib/auth-context"

interface NFT {
  _id: string
  name: string
  imageUrl: string
  description: string
  brand?: string
  year?: string
  serialNumber?: string
  createdAt: string
  creatorId: string
  ownerId: string
  ownershipHistory: {
    userId: string
    userName: string
    transferredAt: string
    transferredFrom?: string
    transferredFromName?: string
  }[]
}

export default function ItemDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const { user } = useAuth()
  const [nft, setNft] = useState<NFT | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const id = params?.id as string

  useEffect(() => {
    if (id) {
      fetchNftDetails(id)
    }
  }, [id])

  const fetchNftDetails = async (nftId: string) => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/nft/${nftId}`)
      const data = await response.json()

      if (data.success) {
        setNft(data.nft)
      } else {
        throw new Error(data.message || "Failed to fetch NFT details")
      }
    } catch (error) {
      console.error("Error fetching NFT details:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to fetch NFT details",
        variant: "destructive",
      })
      router.push("/hala-app/wallet")
    } finally {
      setIsLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  const goBack = () => {
    router.back()
  }

  if (isLoading) {
    return (
      <PageTransition>
        <div className="flex justify-center items-center min-h-[60vh]">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        </div>
      </PageTransition>
    )
  }

  if (!nft) {
    return (
      <PageTransition>
        <div className="text-center py-12">
          <p className="text-gray-500 mb-4">Item not found</p>
          <Button variant="outline" onClick={goBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Go Back
          </Button>
        </div>
      </PageTransition>
    )
  }


  return (
    <PageTransition>
      <div className="space-y-6">
        <Button variant="ghost" onClick={goBack} className="mb-4 -ml-2">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <div className="rounded-lg overflow-hidden border border-gray-100 bg-white">
              <img
                src={nft.imageUrl || "/placeholder.svg"}
                alt={nft.name}
                className="w-full aspect-square object-cover"
              />
            </div>
          </div>

          <div className="space-y-6">
            <div>
              <h1 className="text-2xl md:text-3xl font-medium">{nft.name}</h1>
              {nft.brand && <p className="text-gray-500 mt-1">{nft.brand}</p>}
            </div>

            <Card className="p-5 space-y-4">
              {nft.serialNumber && (
                <div className="flex items-center">
                  <Tag className="h-5 w-5 mr-3 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">Serial Number</p>
                    <p className="font-medium">{nft.serialNumber}</p>
                  </div>
                </div>
              )}

              {nft.year && (
                <div className="flex items-center">
                  <Calendar className="h-5 w-5 mr-3 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">Year</p>
                    <p className="font-medium">{nft.year}</p>
                  </div>
                </div>
              )}

              <div className="flex items-center">
                <Info className="h-5 w-5 mr-3 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-500">Created</p>
                  <p className="font-medium">{formatDate(nft.createdAt)}</p>
                </div>
              </div>
            </Card>

            {nft.description && (
              <div>
                <h2 className="text-lg font-medium mb-2">Description</h2>
                <p className="text-gray-600">{nft.description}</p>
              </div>
            )}

            <Button
              className="w-full bg-black hover:bg-gray-800 text-white rounded-full text-sm font-medium uppercase tracking-wider h-12"
              onClick={() => {
                if (user?.role === "business") {
                  router.push(`/hala-app/business/transfers?nftId=${nft._id}`)
                } else {
                  router.push(`/hala-app/transfers?nftId=${nft._id}`)
                }
              }}
            >
              Transfer This Item
            </Button>
          </div>
        </div>

        <div className="mt-8">
          <h2 className="text-lg font-medium mb-4">Ownership History</h2>
          <Card className="p-5">
            {nft.ownershipHistory && nft.ownershipHistory.length > 0 ? (
              <div className="space-y-4">
                {nft.ownershipHistory.map((record, index) => (
                  <div key={index} className="flex items-start">
                    <User className="h-5 w-5 mr-3 text-gray-400 mt-0.5" />
                    <div>
                      <p className="font-medium">{record.userName}</p>
                      <p className="text-sm text-gray-500">
                        {index === 0 ? "Original owner" : `Received from ${record.transferredFromName || "Unknown"}`}
                      </p>
                      <p className="text-xs text-gray-400 mt-1">{formatDate(record.transferredAt)}</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">No ownership history available</p>
            )}
          </Card>
        </div>
      </div>
    </PageTransition>
  )
}
