import { NextResponse } from "next/server"
import clientPromise from "@/lib/mongodb"
import bcrypt from "bcryptjs"
import jwt from "jsonwebtoken"
import { cookies } from "next/headers"
import type { User, UserSession } from "@/lib/models/user"
import { checkPasswordStrength } from "@/lib/password-utils"

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key"

export async function POST(request: Request) {
  try {
    const { email, password, name, role, businessName, businessVatNumber } = await request.json()

    if (!email || !password || !name || !role) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    const passwordCheck = checkPasswordStrength(password)
    if (!passwordCheck.isStrong) {
      return NextResponse.json({ error: `Password is not strong enough: ${passwordCheck.feedback}` }, { status: 400 })
    }

    if (role === "business" && (!businessName || !businessVatNumber)) {
      return NextResponse.json(
        { error: "Business name and VAT number are required for business accounts" },
        { status: 400 },
      )
    }

    const client = await clientPromise
    const db = client.db()
    const usersCollection = db.collection("users")

    const existingUser = await usersCollection.findOne({ email })
    if (existingUser) {
      return NextResponse.json({ error: "Email already in use" }, { status: 409 })
    }

    const hashedPassword = await bcrypt.hash(password, 10)

    const newUser: User = {
      email,
      password: hashedPassword,
      name,
      role,
      createdAt: new Date(),
      updatedAt: new Date(),
      isVerified: false,
    }

    if (role === "business") {
      newUser.businessName = businessName
      newUser.businessVatNumber = businessVatNumber
    }

    const result = await usersCollection.insertOne(newUser)
    const userId = result.insertedId

    const session: UserSession = {
      id: userId.toString(),
      email: newUser.email,
      name: newUser.name,
      role: newUser.role,
      businessName: newUser.businessName,
    }

    const token = jwt.sign(session, JWT_SECRET, { expiresIn: "7d" })

    cookies().set({
      name: "auth-token",
      value: token,
      httpOnly: true,
      path: "/",
      secure: process.env.NODE_ENV === "production",
      maxAge: 60 * 60 * 24 * 7,
    })

    return NextResponse.json({ user: session })
  } catch (error) {
    console.error("Signup error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
