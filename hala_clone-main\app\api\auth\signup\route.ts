import { NextResponse } from "next/server"
import { userService } from "@/lib/database"
import bcrypt from "bcryptjs"
import jwt from "jsonwebtoken"
import { cookies } from "next/headers"
import type { User, UserSession } from "@/lib/models/user"
import { checkPasswordStrength } from "@/lib/password-utils"

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key"

export async function POST(request: Request) {
  try {
    const { email, password, name, role, business_name, business_vat_number } = await request.json()

    if (!email || !password || !name || !role) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    const passwordCheck = checkPasswordStrength(password)
    if (!passwordCheck.isStrong) {
      return NextResponse.json({ error: `Password is not strong enough: ${passwordCheck.feedback}` }, { status: 400 })
    }

    if (role === "business" && (!business_name || !business_vat_number)) {
      return NextResponse.json(
        { error: "Business name and VAT number are required for business accounts" },
        { status: 400 },
      )
    }

    const existingUser = await userService.findByEmail(email)
    if (existingUser) {
      return NextResponse.json({ error: "Email already in use" }, { status: 409 })
    }

    const hashedPassword = await bcrypt.hash(password, 10)

    const newUserData = {
      email,
      password: hashedPassword,
      name,
      role,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_verified: false,
      ...(role === "business" && {
        business_name,
        business_vat_number
      })
    }

    const newUser = await userService.create(newUserData)

    const session: UserSession = {
      id: newUser.id!,
      email: newUser.email,
      name: newUser.name,
      role: newUser.role,
      business_name: newUser.business_name,
    }

    const token = jwt.sign(session, JWT_SECRET, { expiresIn: "7d" })

    cookies().set({
      name: "auth-token",
      value: token,
      httpOnly: true,
      path: "/",
      secure: process.env.NODE_ENV === "production",
      maxAge: 60 * 60 * 24 * 7,
    })

    return NextResponse.json({ user: session })
  } catch (error) {
    console.error("Signup error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
