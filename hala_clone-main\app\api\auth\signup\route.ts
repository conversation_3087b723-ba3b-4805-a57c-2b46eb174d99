import { NextResponse } from "next/server"
import { authService } from "@/lib/auth"
import type { SignupData } from "@/lib/models/user"
import { checkPasswordStrength } from "@/lib/password-utils"

export async function POST(request: Request) {
  try {
    const { email, password, full_name, role, business_name, business_vat_number } = await request.json()

    if (!email || !password || !full_name || !role) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    const passwordCheck = checkPasswordStrength(password)
    if (!passwordCheck.isStrong) {
      return NextResponse.json({ error: `Password is not strong enough: ${passwordCheck.feedback}` }, { status: 400 })
    }

    if (role === "business" && (!business_name || !business_vat_number)) {
      return NextResponse.json(
        { error: "Business name and VAT number are required for business accounts" },
        { status: 400 },
      )
    }

    const signupData: SignupData = {
      email,
      password,
      full_name,
      role,
      business_name,
      business_vat_number
    }

    const { user, profile } = await authService.signUp(signupData)

    if (!profile) {
      return NextResponse.json({ error: "Failed to create user profile" }, { status: 500 })
    }

    const session = authService.profileToSession(profile)

    return NextResponse.json({
      user: session,
      success: true,
      message: "Account created successfully"
    })
  } catch (error) {
    console.error("Signup error:", error)
    const message = error instanceof Error ? error.message : "Internal server error"
    return NextResponse.json({ error: message }, { status: 500 })
  }
}
