# Supabase Migration Guide

This guide explains how to migrate the Hala project from MongoDB to Supabase.

## 🚀 Quick Setup

### 1. Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and create a new project
2. Wait for the project to be fully provisioned
3. Go to Settings > API to get your project credentials

### 2. Set Up Environment Variables

Copy `.env.example` to `.env.local` and fill in your Supabase credentials:

```bash
cp .env.example .env.local
```

Update the following variables:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
JWT_SECRET=your_jwt_secret_key
```

### 3. Run Database Schema

1. Go to your Supabase project dashboard
2. Navigate to SQL Editor
3. Copy and paste the contents of `supabase-schema.sql`
4. Run the SQL to create all tables and policies

### 4. Install Dependencies

```bash
npm install
```

### 5. Start the Application

```bash
npm run dev
```

## 📊 Database Schema

### Tables Created

1. **users** - User accounts (customers and businesses)
2. **items** - Digital items/assets (formerly nfts)
3. **ownership_history** - Track ownership changes
4. **transfers** - Transfer history between users

### Key Changes from MongoDB

- **ObjectId → UUID**: All IDs are now UUIDs instead of MongoDB ObjectIds
- **Snake Case**: Database columns use snake_case (e.g., `created_at` instead of `createdAt`)
- **Normalized Data**: Ownership history is now in a separate table
- **Row Level Security**: Built-in security policies for data access

## 🔄 API Changes

### Backward Compatibility

The API endpoints maintain backward compatibility by:
- Converting between snake_case (database) and camelCase (API) formats
- Mapping new field names to old field names where needed
- Maintaining the same response structures

### Updated Routes

All the following routes have been updated to use Supabase:

- `POST /api/auth/signup` - User registration
- `POST /api/auth/login` - User authentication
- `POST /api/nft/mint` - Create new items
- `GET /api/nft/my-nfts` - Get user's items
- `POST /api/nft/transfer` - Transfer items between users

## 🛠️ Development Notes

### Database Service Layer

A new database service layer (`lib/database.ts`) provides:
- Type-safe database operations
- Consistent error handling
- Backward compatibility helpers
- Centralized database logic

### Type Definitions

Updated type definitions in `lib/models/`:
- `User` interface updated for Supabase schema
- `Item` interface (new) with `NFT` interface for compatibility
- All timestamps are now ISO strings instead of Date objects

### Security

- Row Level Security (RLS) enabled on all tables
- Users can only access their own data
- Business users can create items
- Proper authentication checks on all endpoints

## 🔧 Troubleshooting

### Common Issues

1. **Environment Variables**: Make sure all Supabase credentials are correctly set
2. **Database Schema**: Ensure the SQL schema has been run completely
3. **RLS Policies**: Check that Row Level Security policies are properly configured
4. **CORS Issues**: Verify your domain is added to Supabase allowed origins

### Migration from Existing MongoDB Data

If you have existing MongoDB data to migrate:

1. Export your MongoDB collections
2. Transform the data to match the new schema
3. Import using Supabase's bulk import tools or custom scripts

## 📈 Benefits of Supabase

- **Real-time subscriptions** for live updates
- **Built-in authentication** (can replace custom JWT)
- **Automatic API generation** with type safety
- **Row Level Security** for data protection
- **PostgreSQL features** like JSON columns, full-text search
- **Scalable infrastructure** with automatic backups

## 🎯 Next Steps

1. **Authentication Migration**: Consider using Supabase Auth instead of custom JWT
2. **Real-time Features**: Add real-time subscriptions for live updates
3. **File Storage**: Use Supabase Storage for item images
4. **Edge Functions**: Move API logic to Supabase Edge Functions
5. **Analytics**: Use Supabase's built-in analytics and logging
