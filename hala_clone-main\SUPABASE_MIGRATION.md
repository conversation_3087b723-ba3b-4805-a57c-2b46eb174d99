# Supabase Migration Guide

This guide explains how to migrate the Hala project from MongoDB to Supabase with full Supabase Auth integration.

## 🚀 Quick Setup

### 1. Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and create a new project
2. Wait for the project to be fully provisioned
3. Go to Settings > API to get your project credentials

### 2. Set Up Environment Variables

Copy `.env.example` to `.env.local` and fill in your Supabase credentials:

```bash
cp .env.example .env.local
```

Update the following variables:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### 3. Run Database Schema

1. Go to your Supabase project dashboard
2. Navigate to SQL Editor
3. Copy and paste the contents of `supabase-schema.sql`
4. Run the SQL to create all tables and policies

**Important**: The schema creates:
- `profiles` table that extends the built-in `auth.users` table
- Automatic profile creation trigger when users sign up
- Row Level Security (RLS) policies for data protection
- All necessary foreign key relationships

### 4. Install Dependencies

```bash
npm install
```

### 5. Start the Application

```bash
npm run dev
```

## 📊 Database Schema

### Tables Created

1. **profiles** - User profiles (extends auth.users)
2. **items** - Digital items/assets (formerly nfts)
3. **ownership_history** - Track ownership changes
4. **transfers** - Transfer history between users

### Key Changes from MongoDB

- **Supabase Auth**: Uses built-in Supabase authentication instead of custom JWT
- **ObjectId → UUID**: All IDs are now UUIDs instead of MongoDB ObjectIds
- **Snake Case**: Database columns use snake_case (e.g., `created_at` instead of `createdAt`)
- **Normalized Data**: Ownership history is now in a separate table
- **Row Level Security**: Built-in security policies for data access
- **Auto Profile Creation**: Profiles are automatically created when users sign up

## 🔄 Authentication Changes

### Supabase Auth Integration

The application now uses Supabase's built-in authentication system:

- **Client-side**: React context provider with automatic session management
- **Server-side**: Middleware for route protection and session validation
- **API Routes**: Server-side auth validation using Supabase client
- **Auto Profile Creation**: Database trigger creates user profiles automatically

### Frontend Auth

- **AuthProvider**: React context for managing auth state
- **useAuth Hook**: Access user, profile, signUp, signIn, signOut functions
- **Middleware**: Automatic route protection and redirects
- **Session Management**: Automatic token refresh and persistence

### Updated Routes

All the following routes have been updated to use Supabase Auth:

- `POST /api/auth/signup` - User registration with Supabase Auth
- `POST /api/auth/login` - User authentication with Supabase Auth
- `POST /api/auth/logout` - Sign out with Supabase Auth
- `POST /api/nft/mint` - Create new items (auth required)
- `GET /api/nft/my-nfts` - Get user's items (auth required)
- `POST /api/nft/transfer` - Transfer items between users (auth required)

## 🛠️ Development Notes

### Database Service Layer

A new database service layer (`lib/database.ts`) provides:
- Type-safe database operations
- Consistent error handling
- Backward compatibility helpers
- Centralized database logic

### Type Definitions

Updated type definitions in `lib/models/`:
- `User` interface updated for Supabase schema
- `Item` interface (new) with `NFT` interface for compatibility
- All timestamps are now ISO strings instead of Date objects

### Security

- Row Level Security (RLS) enabled on all tables
- Users can only access their own data
- Business users can create items
- Proper authentication checks on all endpoints

## 🔧 Troubleshooting

### Common Issues

1. **Environment Variables**: Make sure all Supabase credentials are correctly set
2. **Database Schema**: Ensure the SQL schema has been run completely
3. **RLS Policies**: Check that Row Level Security policies are properly configured
4. **CORS Issues**: Verify your domain is added to Supabase allowed origins

### Migration from Existing MongoDB Data

If you have existing MongoDB data to migrate:

1. Export your MongoDB collections
2. Transform the data to match the new schema
3. Import using Supabase's bulk import tools or custom scripts

## 📈 Benefits of Supabase

- **Real-time subscriptions** for live updates
- **Built-in authentication** (can replace custom JWT)
- **Automatic API generation** with type safety
- **Row Level Security** for data protection
- **PostgreSQL features** like JSON columns, full-text search
- **Scalable infrastructure** with automatic backups

## ✅ Verification Checklist

### Database Setup Verification

1. **Check Tables Created:**
   ```sql
   -- Run in Supabase SQL Editor to verify tables exist
   SELECT table_name FROM information_schema.tables
   WHERE table_schema = 'public'
   AND table_name IN ('profiles', 'items', 'ownership_history', 'transfers');
   ```

2. **Verify RLS Policies:**
   ```sql
   -- Check RLS is enabled
   SELECT schemaname, tablename, rowsecurity
   FROM pg_tables
   WHERE schemaname = 'public'
   AND tablename IN ('profiles', 'items', 'ownership_history', 'transfers');
   ```

3. **Test Profile Creation Trigger:**
   ```sql
   -- Check if trigger exists
   SELECT trigger_name, event_manipulation, event_object_table
   FROM information_schema.triggers
   WHERE trigger_name = 'on_auth_user_created';
   ```

### Authentication Flow Verification

1. **Test User Registration:**
   - Go to `/auth/signup`
   - Create a customer account
   - Create a business account
   - Verify profiles are created automatically

2. **Test User Login:**
   - Go to `/auth/login`
   - Login with created accounts
   - Verify redirect to dashboard

3. **Test Route Protection:**
   - Try accessing `/hala-app/dashboard` without login
   - Should redirect to `/auth/login`

4. **Test API Authentication:**
   - Try accessing protected API routes
   - Should return 401 without authentication

### Database Relationships Verification

1. **Profiles Table:**
   - Extends `auth.users` with foreign key
   - Contains business fields for business accounts
   - Has proper RLS policies

2. **Items Table:**
   - References `profiles` table for creator/owner
   - Has proper RLS policies for access control

3. **Ownership History:**
   - Tracks item ownership changes
   - References both items and profiles

4. **Transfers:**
   - Manages item transfers between users
   - Proper sender/recipient relationships

## 🎯 Next Steps

1. **Authentication Migration**: ✅ Complete - Using Supabase Auth
2. **Real-time Features**: Add real-time subscriptions for live updates
3. **File Storage**: Use Supabase Storage for item images
4. **Edge Functions**: Move API logic to Supabase Edge Functions
5. **Analytics**: Use Supabase's built-in analytics and logging

## 🚀 Ready for Production

The project is now completely free of Web3 dependencies and ready for production with:
- ✅ **Supabase Auth** - Built-in authentication system
- ✅ **PostgreSQL Database** - Scalable and reliable
- ✅ **Row Level Security** - Database-level security
- ✅ **Type Safety** - Full TypeScript support
- ✅ **Auto Profile Management** - Seamless user onboarding
