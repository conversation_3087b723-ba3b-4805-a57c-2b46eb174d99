"use client"

import { type FC, type <PERSON>actNode, useCallback, useEffect } from "react"
import { ConnectionProvider, WalletProvider } from "@solana/wallet-adapter-react"
import { WalletAdapterNetwork } from "@solana/wallet-adapter-base"
import { PhantomWalletAdapter, SolflareWalletAdapter } from "@solana/wallet-adapter-wallets"
import { WalletModalProvider } from "@solana/wallet-adapter-react-ui"
import { clusterApiUrl } from "@solana/web3.js"
import Cookies from "js-cookie"
import { useWallet } from "@solana/wallet-adapter-react"

import "@solana/wallet-adapter-react-ui/styles.css"

interface WalletContextProviderProps {
  children: ReactNode
}

export const WalletContextProvider: FC<WalletContextProviderProps> = ({ children }) => {
  const network = WalletAdapterNetwork.Devnet

  const endpoint = clusterApiUrl(network)

  const wallets = [new PhantomWalletAdapter(), new SolflareWalletAdapter()]

  return (
    <ConnectionProvider endpoint={endpoint}>
      <WalletProvider wallets={wallets} autoConnect>
        <WalletModalProvider>
          <WalletAddressTracker>{children}</WalletAddressTracker>
        </WalletModalProvider>
      </WalletProvider>
    </ConnectionProvider>
  )
}

const WalletAddressTracker: FC<{ children: ReactNode }> = ({ children }) => {
  const { publicKey, connected } = useWallet()

  const saveWalletAddress = useCallback(() => {
    if (connected && publicKey) {
      Cookies.set("walletAddress", publicKey.toString(), { expires: 7 })
      console.log("Wallet address saved to cookie:", publicKey.toString())
    } else if (!connected) {
      Cookies.remove("walletAddress")
      console.log("Wallet disconnected, cookie removed")
    }
  }, [publicKey, connected])

  useEffect(() => {
    saveWalletAddress()
  }, [saveWalletAddress])

  return <>{children}</>
}
