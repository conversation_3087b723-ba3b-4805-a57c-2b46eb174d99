import { NextResponse } from "next/server"
import { cookies } from "next/headers"
import jwt from "jsonwebtoken"
import { itemService, userService, transferService, ownershipService } from "@/lib/database"
import type { NFTTransfer } from "@/lib/models/nft"
import type { UserSession } from "@/lib/models/user"

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key"

export async function POST(request: Request) {
  try {
    const token = cookies().get("auth-token")?.value
    if (!token) {
      return NextResponse.json({ success: false, message: "Unauthorized" }, { status: 401 })
    }

    let user: UserSession
    try {
      user = jwt.verify(token, JWT_SECRET) as UserSession
    } catch (error) {
      return NextResponse.json({ success: false, message: "Invalid token" }, { status: 401 })
    }

    const body = await request.json()
    const { nftId, recipientEmail, message } = body as NFTTransfer

    if (!nftId || !recipientEmail) {
      return NextResponse.json({ success: false, message: "NFT ID and recipient email are required" }, { status: 400 })
    }

    const item = await itemService.findById(nftId)

    if (!item || item.owner_id !== user.id || !item.is_active) {
      return NextResponse.json({ success: false, message: "Item not found or you don't own it" }, { status: 404 })
    }

    let recipient = await userService.findByEmail(recipientEmail)

    if (!recipient) {
      const newUserData = {
        email: recipientEmail,
        name: recipientEmail.split("@")[0],
        role: "customer" as const,
        password: "", // Will need to be set when user registers
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        is_verified: false,
      }

      recipient = await userService.create(newUserData)
    }

    // Update item ownership
    await itemService.updateOwner(nftId, recipient.id!)

    // Add ownership history record
    await ownershipService.addRecord({
      item_id: nftId,
      user_id: recipient.id!,
      user_name: recipient.name,
      transferred_from: user.id,
      transferred_from_name: user.name,
    })

    // Create transfer record
    await transferService.create({
      item_id: nftId,
      item_name: item.name,
      sender_id: user.id,
      sender_name: user.name,
      sender_email: user.email,
      recipient_id: recipient.id!,
      recipient_email: recipientEmail,
      message: message || "",
      status: "completed",
    })

    return NextResponse.json({
      success: true,
      message: "Item transferred successfully",
    })
  } catch (error) {
    console.error("Error transferring NFT:", error)
    return NextResponse.json({ success: false, message: "Failed to transfer item" }, { status: 500 })
  }
}
