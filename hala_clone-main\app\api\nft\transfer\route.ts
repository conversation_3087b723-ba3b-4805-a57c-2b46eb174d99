import { NextResponse } from "next/server"
import { cookies } from "next/headers"
import jwt from "jsonwebtoken"
import clientPromise from "@/lib/mongodb"
import { ObjectId } from "mongodb"
import type { NFTTransfer } from "@/lib/models/nft"
import type { UserSession } from "@/lib/models/user"

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key"

export async function POST(request: Request) {
  try {
    const token = cookies().get("auth-token")?.value
    if (!token) {
      return NextResponse.json({ success: false, message: "Unauthorized" }, { status: 401 })
    }

    let user: UserSession
    try {
      user = jwt.verify(token, JWT_SECRET) as UserSession
    } catch (error) {
      return NextResponse.json({ success: false, message: "Invalid token" }, { status: 401 })
    }

    const body = await request.json()
    const { nftId, recipientEmail, message } = body as NFTTransfer

    if (!nftId || !recipientEmail) {
      return NextResponse.json({ success: false, message: "NFT ID and recipient email are required" }, { status: 400 })
    }

    const client = await clientPromise
    const db = client.db()

    const nft = await db.collection("nfts").findOne({
      _id: new ObjectId(nftId),
      ownerId: new ObjectId(user.id),
      isActive: true,
    })

    if (!nft) {
      return NextResponse.json({ success: false, message: "NFT not found or you don't own it" }, { status: 404 })
    }

    let recipient = await db.collection("users").findOne({ email: recipientEmail })

    if (!recipient) {
      const newUser = {
        email: recipientEmail,
        name: recipientEmail.split("@")[0],
        role: "customer",
        createdAt: new Date(),
        updatedAt: new Date(),
        isVerified: false,
        pendingRegistration: true,
      }

      const result = await db.collection("users").insertOne(newUser)
      recipient = { ...newUser, _id: result.insertedId }
    }

    const updateResult = await db.collection("nfts").updateOne(
      { _id: new ObjectId(nftId) },
      {
        $set: {
          ownerId: recipient._id,
        },
        $push: {
          ownershipHistory: {
            userId: recipient._id,
            userName: recipient.name,
            transferredAt: new Date(),
            transferredFrom: new ObjectId(user.id),
            transferredFromName: user.name,
          },
        },
      },
    )

    if (updateResult.modifiedCount === 0) {
      return NextResponse.json({ success: false, message: "Failed to transfer NFT" }, { status: 500 })
    }

    await db.collection("transfers").insertOne({
      nftId: new ObjectId(nftId),
      nftName: nft.name,
      senderId: new ObjectId(user.id),
      senderName: user.name,
      senderEmail: user.email,
      recipientId: recipient._id,
      recipientEmail: recipientEmail,
      message: message || "",
      transferredAt: new Date(),
      status: "pending",
    })

    return NextResponse.json({
      success: true,
      message: "NFT transferred successfully",
    })
  } catch (error) {
    console.error("Error transferring NFT:", error)
    return NextResponse.json({ success: false, message: "Failed to transfer NFT" }, { status: 500 })
  }
}
