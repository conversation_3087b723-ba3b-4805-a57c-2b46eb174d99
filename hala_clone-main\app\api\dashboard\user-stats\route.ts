import { NextResponse } from "next/server"
import { cookies } from "next/headers"
import jwt from "jsonwebtoken"
import clientPromise from "@/lib/mongodb"
import { ObjectId } from "mongodb"
import type { UserSession } from "@/lib/models/user"

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key"

export async function GET() {
  try {
    const token = (await cookies()).get("auth-token")?.value
    if (!token) {
      return NextResponse.json({ success: false, message: "Unauthorized" }, { status: 401 })
    }

    let user: UserSession
    try {
      user = jwt.verify(token, JWT_SECRET) as UserSession
    } catch (error) {
      return NextResponse.json({ success: false, message: "Invalid token" }, { status: 401 })
    }

    const client = await clientPromise
    const db = client.db()

    const tokensCount = await db.collection("nfts").countDocuments({
      ownerId: new ObjectId(user.id),
      isActive: true,
    })

    const totalUsers = await db.collection("users").countDocuments({})

    const businessUsers = await db.collection("users").countDocuments({ role: "business" })

    const recentActivity = await db
      .collection("transfers")
      .find({
        $or: [{ senderId: new ObjectId(user.id) }, { recipientId: new ObjectId(user.id) }],
      })
      .sort({ transferredAt: -1 })
      .limit(5)
      .toArray()

    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

    const receivedNfts = await db
      .collection("transfers")
      .find({
        recipientId: new ObjectId(user.id),
        transferredAt: { $gte: sevenDaysAgo },
      })
      .toArray()

    const nftsByDay: Record<string, number> = {}
    for (let i = 0; i < 7; i++) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      const dateString = date.toISOString().split("T")[0]
      nftsByDay[dateString] = 0
    }

    receivedNfts.forEach((nft) => {
      const date = new Date(nft.transferredAt).toISOString().split("T")[0]
      if (nftsByDay[date] !== undefined) {
        nftsByDay[date]++
      }
    })

    const marketOverview = Object.entries(nftsByDay)
      .map(([date, count]) => ({
        date,
        count,
      }))
      .reverse()

    return NextResponse.json({
      success: true,
      stats: {
        tokensCount,
        totalUsers,
        businessUsers,
        recentActivity,
        marketOverview,
      },
    })
  } catch (error) {
    console.error("Error fetching user stats:", error)
    return NextResponse.json({ success: false, message: "Failed to fetch user stats" }, { status: 500 })
  }
}
