"use client"

import { type FC, useCallback, useEffect, useState } from "react"
import { useWallet } from "@solana/wallet-adapter-react"
import { WalletMultiButton } from "@solana/wallet-adapter-react-ui"
import { Button } from "@/components/ui/button"
import Cookies from "js-cookie"

export const WalletConnectButton: FC = () => {
  const { publicKey, connected, disconnect } = useWallet()
  const [walletAddress, setWalletAddress] = useState<string | null>(null)

  const formatWalletAddress = useCallback((address: string) => {
    return `${address.slice(0, 4)}...${address.slice(-4)}`
  }, [])

  useEffect(() => {
    if (connected && publicKey) {
      setWalletAddress(publicKey.toString())
    } else {
      setWalletAddress(null)
    }
  }, [publicKey, connected])

  useEffect(() => {
    const savedAddress = Cookies.get("walletAddress")
    if (savedAddress && !walletAddress) {
      setWalletAddress(savedAddress)
    }
  }, [walletAddress])

  const walletButtonStyle = {
    background: "linear-gradient(to right, #1a1a1a, #000000)",
    color: "white",
    borderRadius: "9999px",
    fontSize: "0.875rem",
    fontWeight: "500",
    padding: "0.5rem 1rem",
    border: "none",
    cursor: "pointer",
    transition: "all 0.3s",
    height: "40px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    textTransform: "uppercase",
    letterSpacing: "0.05em",
    boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
  }

  return (
    <>
      {!connected ? (
        <WalletMultiButton style={walletButtonStyle} className="wallet-adapter-button">
          CONNECT WALLET
        </WalletMultiButton>
      ) : (
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-600 hidden md:inline">
            {walletAddress ? formatWalletAddress(walletAddress) : "Connected"}
          </span>
          <Button
            variant="outline"
            className="rounded-full text-sm font-medium uppercase tracking-wider"
            onClick={() => disconnect()}
          >
            Disconnect
          </Button>
        </div>
      )}
    </>
  )
}
