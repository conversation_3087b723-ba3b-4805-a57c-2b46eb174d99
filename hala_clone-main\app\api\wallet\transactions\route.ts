import { NextResponse } from "next/server"
import { Connection, PublicKey, LAMPORTS_PER_SOL } from "@solana/web3.js"

const RPC_ENDPOINT = "https://mainnet.helius-rpc.com/?api-key=183b47b0-ca54-4f6b-b7cd-cdca02c574a6"

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const address = searchParams.get("address")

    if (!address) {
      return NextResponse.json({ error: "Wallet address is required" }, { status: 400 })
    }

    const connection = new Connection(RPC_ENDPOINT)

    try {
      const publicKey = new PublicKey(address)

      const signatures = await connection.getSignaturesForAddress(publicKey, { limit: 10 })

      if (signatures.length === 0) {
        return NextResponse.json({ transactions: [] })
      }

      const transactions = await Promise.all(
        signatures.map(async (sig) => {
          try {
            const tx = await connection.getParsedTransaction(sig.signature, {
              maxSupportedTransactionVersion: 0,
            })

            if (!tx) return null

            const accountKeys = tx.transaction.message.accountKeys
            const walletIndex = accountKeys.findIndex((key) => key.pubkey.toString() === address)
            const isSender = walletIndex !== -1 && accountKeys[walletIndex].signer

            let amount = "Unknown"
            if (tx.meta && tx.meta.preBalances && tx.meta.postBalances) {
              if (walletIndex !== -1) {
                const preBalance = tx.meta.preBalances[walletIndex]
                const postBalance = tx.meta.postBalances[walletIndex]
                const difference = Math.abs(preBalance - postBalance) / LAMPORTS_PER_SOL

                if (difference > 0.000001) {
                  amount = difference.toFixed(4)
                }
              }
            }

            return {
              signature: sig.signature,
              type: isSender ? "Sent" : "Received",
              amount,
              date: new Date(sig.blockTime ? sig.blockTime * 1000 : Date.now()).toISOString(),
              status: tx.meta?.err ? "failed" : "confirmed",
              to: isSender ? accountKeys.find((key) => !key.signer)?.pubkey.toString() : undefined,
              from: !isSender ? accountKeys.find((key) => key.signer)?.pubkey.toString() : undefined,
            }
          } catch (error) {
            console.error("Error processing transaction:", sig.signature, error)
            return null
          }
        }),
      )

      return NextResponse.json({
        transactions: transactions.filter((tx) => tx !== null),
      })
    } catch (error) {
      console.error("Error fetching transactions:", error)
      return NextResponse.json({ error: "Invalid wallet address or RPC error" }, { status: 400 })
    }
  } catch (error) {
    console.error("Server error:", error)
    return NextResponse.json({ error: "Server error" }, { status: 500 })
  }
}
