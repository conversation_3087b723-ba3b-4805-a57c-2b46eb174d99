"use client"

import { useState, useEffect } from "react"
import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { motion } from "framer-motion"
import {
  BarChart3,
  Globe,
  RefreshCw,
  ArrowUpRight,
  Users,
  ShoppingBag,
  DollarSign,
  Calendar,
  Download,
  PieChart,
  LineChart,
  Loader2,
  TrendingUp,
} from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { PageTransition } from "@/components/page-transition"
import { useToast } from "@/components/ui/use-toast"

interface AnalyticsData {
  itemsSold: number
  newCustomers: number
  totalRevenue: number
  avgDailyTrades: number
  salesOverTime: { date: string; count: number }[]
  distributionByCategory: { category: string; count: number }[]
  topSellingItems: { id: string; name: string; sales: number; revenue: number }[]
  topCustomers: { id: string; name: string; purchases: number; spent: number }[]
}

export default function BusinessAnalytics() {
  const [isLoading, setIsLoading] = useState(true)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [timeRange, setTimeRange] = useState("30d")
  const [activeTab, setActiveTab] = useState("overview")
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    fetchAnalytics(timeRange)
  }, [timeRange])

  const fetchAnalytics = async (range: string) => {
    try {
      setIsRefreshing(true)
      const response = await fetch(`/api/dashboard/analytics?timeRange=${range}`)
      const data = await response.json()

      if (data.success) {
        setAnalytics(data.analytics)
      } else {
        throw new Error(data.message || "Failed to fetch analytics")
      }
    } catch (error) {
      console.error("Error fetching analytics:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to load analytics data",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
      setIsRefreshing(false)
    }
  }

  const refreshData = () => {
    fetchAnalytics(timeRange)
  }

  if (isLoading) {
    return (
      <PageTransition>
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        </div>
      </PageTransition>
    )
  }

  return (
    <PageTransition>
      <div className="space-y-6">
        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl md:text-3xl font-light">Analytics</h1>
              <p className="text-gray-500 mt-2 text-sm md:text-base">
                Analyze the performance of your tokenized assets
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="90d">Last 90 days</SelectItem>
                  <SelectItem value="1y">Last year</SelectItem>
                  <SelectItem value="all">All time</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="icon" onClick={refreshData} disabled={isRefreshing}>
                <RefreshCw className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`} />
              </Button>
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </motion.div>

        <Tabs defaultValue="overview" className="space-y-6" onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="sales">Sales</TabsTrigger>
            <TabsTrigger value="geography">Geography</TabsTrigger>
            <TabsTrigger value="items">Items</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Main statistics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <Card className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div className="p-2 bg-blue-50 rounded-full">
                      <ShoppingBag className="h-5 w-5 text-blue-500" />
                    </div>
                    <span className="text-green-500 flex items-center text-xs bg-green-50 px-2 py-1 rounded-full">
                      +12% <ArrowUpRight className="h-3 w-3 ml-1" />
                    </span>
                  </div>
                  <h3 className="text-gray-500 text-sm">Items Sold</h3>
                  <p className="text-3xl font-light mt-1">{analytics?.itemsSold || 0}</p>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <Card className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div className="p-2 bg-purple-50 rounded-full">
                      <Users className="h-5 w-5 text-purple-500" />
                    </div>
                    <span className="text-green-500 flex items-center text-xs bg-green-50 px-2 py-1 rounded-full">
                      +23% <ArrowUpRight className="h-3 w-3 ml-1" />
                    </span>
                  </div>
                  <h3 className="text-gray-500 text-sm">New Customers</h3>
                  <p className="text-3xl font-light mt-1">{analytics?.newCustomers || 0}</p>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <Card className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div className="p-2 bg-green-50 rounded-full">
                      <DollarSign className="h-5 w-5 text-green-500" />
                    </div>
                    <span className="text-green-500 flex items-center text-xs bg-green-50 px-2 py-1 rounded-full">
                      +18% <ArrowUpRight className="h-3 w-3 ml-1" />
                    </span>
                  </div>
                  <h3 className="text-gray-500 text-sm">Total Revenue</h3>
                  <p className="text-3xl font-light mt-1">${analytics?.totalRevenue || 0}</p>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <Card className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div className="p-2 bg-amber-50 rounded-full">
                      <Calendar className="h-5 w-5 text-amber-500" />
                    </div>
                    <span className="text-green-500 flex items-center text-xs bg-green-50 px-2 py-1 rounded-full">
                      +5% <ArrowUpRight className="h-3 w-3 ml-1" />
                    </span>
                  </div>
                  <h3 className="text-gray-500 text-sm">Average Daily Trades</h3>
                  <p className="text-3xl font-light mt-1">{analytics?.avgDailyTrades.toFixed(1) || "0"}</p>
                </Card>
              </motion.div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.5 }}
              >
                <Card className="p-6">
                  <div className="flex justify-between items-center mb-6">
                    <h2 className="text-lg font-medium">Sales Over Time</h2>
                    <div className="p-2 bg-gray-50 rounded-full">
                      <LineChart className="h-5 w-5 text-gray-500" />
                    </div>
                  </div>
                  <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                    {analytics?.salesOverTime && analytics.salesOverTime.length > 0 ? (
                      <div className="w-full h-full p-4">
                        <div className="h-full flex items-end justify-between">
                          {analytics.salesOverTime.slice(0, 10).map((day, index) => {
                            const maxValue = Math.max(...analytics.salesOverTime.map((d) => d.count), 1)
                            const heightPercentage = (day.count / maxValue) * 100

                            return (
                              <div key={index} className="flex flex-col items-center">
                                <div
                                  className="w-8 bg-black rounded-t-sm transition-all duration-500 hover:bg-gray-800"
                                  style={{ height: `${heightPercentage || 5}%` }}
                                  title={`${day.date}: ${day.count} sales`}
                                ></div>
                                <div className="text-xs text-gray-500 mt-1">
                                  {new Date(day.date).toLocaleDateString(undefined, { day: "numeric" })}
                                </div>
                              </div>
                            )
                          })}
                        </div>
                      </div>
                    ) : (
                      <div className="text-center">
                        <LineChart className="h-12 w-12 text-gray-300 mx-auto mb-2" />
                        <p className="text-gray-500 text-sm">No sales data available</p>
                      </div>
                    )}
                  </div>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.6 }}
              >
                <Card className="p-6">
                  <div className="flex justify-between items-center mb-6">
                    <h2 className="text-lg font-medium">Distribution by Category</h2>
                    <div className="p-2 bg-gray-50 rounded-full">
                      <PieChart className="h-5 w-5 text-gray-500" />
                    </div>
                  </div>
                  <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                    {analytics?.distributionByCategory && analytics.distributionByCategory.length > 0 ? (
                      <div className="w-full h-full p-4">
                        <div className="grid grid-cols-2 gap-4 h-full">
                          {analytics.distributionByCategory.map((category, index) => {
                            const total = analytics.distributionByCategory.reduce((sum, cat) => sum + cat.count, 0)
                            const percentage = total > 0 ? Math.round((category.count / total) * 100) : 0

                            return (
                              <div key={index} className="flex items-center">
                                <div className="w-4 h-4 rounded-full bg-black mr-2"></div>
                                <div>
                                  <p className="text-sm font-medium">{category.category}</p>
                                  <p className="text-xs text-gray-500">
                                    {percentage}% ({category.count})
                                  </p>
                                </div>
                              </div>
                            )
                          })}
                        </div>
                      </div>
                    ) : (
                      <div className="text-center">
                        <PieChart className="h-12 w-12 text-gray-300 mx-auto mb-2" />
                        <p className="text-gray-500 text-sm">No category data available</p>
                      </div>
                    )}
                  </div>
                </Card>
              </motion.div>
            </div>
          </TabsContent>

          <TabsContent value="sales" className="space-y-6">
            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
              <Card className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-lg font-medium">Sales Trend</h2>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm">
                      Daily
                    </Button>
                    <Button variant="default" size="sm">
                      Weekly
                    </Button>
                    <Button variant="outline" size="sm">
                      Monthly
                    </Button>
                  </div>
                </div>
                <div className="h-80 flex items-center justify-center bg-gray-50 rounded-lg">
                  {analytics?.salesOverTime && analytics.salesOverTime.length > 0 ? (
                    <div className="w-full h-full p-4">
                      <div className="h-full flex items-end justify-between">
                        {analytics.salesOverTime.slice(0, 15).map((day, index) => {
                          const maxValue = Math.max(...analytics.salesOverTime.map((d) => d.count), 1)
                          const heightPercentage = (day.count / maxValue) * 100

                          return (
                            <div key={index} className="flex flex-col items-center">
                              <div
                                className="w-8 bg-black rounded-t-sm transition-all duration-500 hover:bg-gray-800"
                                style={{ height: `${heightPercentage || 5}%` }}
                                title={`${day.date}: ${day.count} sales`}
                              ></div>
                              <div className="text-xs text-gray-500 mt-1">
                                {new Date(day.date).toLocaleDateString(undefined, { day: "numeric" })}
                              </div>
                            </div>
                          )
                        })}
                      </div>
                    </div>
                  ) : (
                    <div className="text-center">
                      <BarChart3 className="h-12 w-12 text-gray-300 mx-auto mb-2" />
                      <p className="text-gray-500 text-sm">No sales data available</p>
                    </div>
                  )}
                </div>
              </Card>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <Card className="p-6">
                  <div className="flex justify-between items-center mb-6">
                    <h2 className="text-lg font-medium">Top Selling Items</h2>
                  </div>
                  <div className="space-y-4">
                    {analytics?.topSellingItems && analytics.topSellingItems.length > 0 ? (
                      analytics.topSellingItems.map((item, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between py-3 border-b border-gray-100 last:border-0"
                        >
                          <div>
                            <p className="text-sm font-medium">{item.name}</p>
                            <p className="text-xs text-gray-500">{item.sales} sales</p>
                          </div>
                          <span className="text-sm font-medium">${item.revenue}</span>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-4 text-gray-500">No sales data available</div>
                    )}
                  </div>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <Card className="p-6">
                  <div className="flex justify-between items-center mb-6">
                    <h2 className="text-lg font-medium">Top Customers</h2>
                  </div>
                  <div className="space-y-4">
                    {analytics?.topCustomers && analytics.topCustomers.length > 0 ? (
                      analytics.topCustomers.map((customer, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between py-3 border-b border-gray-100 last:border-0"
                        >
                          <div>
                            <p className="text-sm font-medium">{customer.name}</p>
                            <p className="text-xs text-gray-500">{customer.purchases} purchases</p>
                          </div>
                          <span className="text-sm font-medium">${customer.spent}</span>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-4 text-gray-500">No customer data available</div>
                    )}
                  </div>
                </Card>
              </motion.div>
            </div>
          </TabsContent>

          <TabsContent value="geography" className="space-y-6">
            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
              <Card className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-lg font-medium">Geographic Distribution</h2>
                </div>
                <div className="h-96 flex items-center justify-center bg-gray-50 rounded-lg mb-6">
                  <div className="text-center">
                    <Globe className="h-12 w-12 text-gray-300 mx-auto mb-2" />
                    <p className="text-gray-500 text-sm">World sales map</p>
                  </div>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-center">
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <p className="text-xs text-gray-500">Europe</p>
                    <p className="font-medium">42%</p>
                    <p className="text-xs text-green-500">+8%</p>
                  </div>
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <p className="text-xs text-gray-500">North America</p>
                    <p className="font-medium">28%</p>
                    <p className="text-xs text-green-500">+12%</p>
                  </div>
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <p className="text-xs text-gray-500">Asia</p>
                    <p className="font-medium">18%</p>
                    <p className="text-xs text-green-500">+15%</p>
                  </div>
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <p className="text-xs text-gray-500">Middle East</p>
                    <p className="font-medium">8%</p>
                    <p className="text-xs text-green-500">+20%</p>
                  </div>
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <p className="text-xs text-gray-500">Others</p>
                    <p className="font-medium">4%</p>
                    <p className="text-xs text-green-500">+5%</p>
                  </div>
                </div>
              </Card>
            </motion.div>
          </TabsContent>

          <TabsContent value="items" className="space-y-6">
            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
              <Card className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-lg font-medium">Item Performance</h2>
                  <div className="flex items-center space-x-2">
                    <Select defaultValue="sales">
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Sort by" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="sales">Sales</SelectItem>
                        <SelectItem value="revenue">Revenue</SelectItem>
                        <SelectItem value="views">Views</SelectItem>
                        <SelectItem value="conversion">Conversion Rate</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-left py-3 px-4 text-sm font-medium text-gray-500">Item</th>
                        <th className="text-center py-3 px-4 text-sm font-medium text-gray-500">Sales</th>
                        <th className="text-center py-3 px-4 text-sm font-medium text-gray-500">Revenue</th>
                        <th className="text-center py-3 px-4 text-sm font-medium text-gray-500">Views</th>
                        <th className="text-center py-3 px-4 text-sm font-medium text-gray-500">Conversion</th>
                        <th className="text-right py-3 px-4 text-sm font-medium text-gray-500">Trend</th>
                      </tr>
                    </thead>
                    <tbody>
                      {analytics?.topSellingItems && analytics.topSellingItems.length > 0 ? (
                        analytics.topSellingItems.map((item, index) => (
                          <tr key={index} className="border-b border-gray-100 last:border-0">
                            <td className="py-3 px-4 text-sm font-medium">{item.name}</td>
                            <td className="py-3 px-4 text-sm text-center">{item.sales}</td>
                            <td className="py-3 px-4 text-sm text-center">${item.revenue}</td>
                            <td className="py-3 px-4 text-sm text-center">{Math.round(item.sales * 50)}</td>
                            <td className="py-3 px-4 text-sm text-center">
                              {((item.sales / (item.sales * 50)) * 100).toFixed(2)}%
                            </td>
                            <td className="py-3 px-4 text-sm text-right">
                              <TrendingUp className="h-4 w-4 text-green-500 inline" />
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan={6} className="py-6 text-center text-gray-500">
                            No item data available
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </Card>
            </motion.div>
          </TabsContent>
        </Tabs>
      </div>
    </PageTransition>
  )
}
