import { NextResponse } from "next/server"
import { cookies } from "next/headers"
import jwt from "jsonwebtoken"
import { itemService, convertToNFTFormat } from "@/lib/database"
import type { UserSession } from "@/lib/models/user"

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key"

export async function GET() {
  try {
    const token = (await cookies()).get("auth-token")?.value
    if (!token) {
      return NextResponse.json({ success: false, message: "Unauthorized" }, { status: 401 })
    }

    let user: UserSession
    try {
      user = jwt.verify(token, JWT_SECRET) as UserSession
    } catch (error) {
      return NextResponse.json({ success: false, message: "Invalid token" }, { status: 401 })
    }

    const items = await itemService.findByOwnerId(user.id, true)

    // Convert to NFT format for backward compatibility
    const nfts = items.map(convertToNFTFormat)

    return NextResponse.json({
      success: true,
      nfts,
    })
  } catch (error) {
    console.error("Error fetching NFTs:", error)
    return NextResponse.json({ success: false, message: "Failed to fetch NFTs" }, { status: 500 })
  }
}
