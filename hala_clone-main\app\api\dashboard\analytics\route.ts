import { NextResponse } from "next/server"
import { cookies } from "next/headers"
import jwt from "jsonwebtoken"
import clientPromise from "@/lib/mongodb"
import { ObjectId } from "mongodb"
import type { UserSession } from "@/lib/models/user"

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key"

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get("timeRange") || "30d"

    const token = cookies().get("auth-token")?.value
    if (!token) {
      return NextResponse.json({ success: false, message: "Unauthorized" }, { status: 401 })
    }

    let user: UserSession
    try {
      user = jwt.verify(token, JWT_SECRET) as UserSession
    } catch (error) {
      return NextResponse.json({ success: false, message: "Invalid token" }, { status: 401 })
    }

    if (user.role !== "business") {
      return NextResponse.json({ success: false, message: "Unauthorized. Business role required" }, { status: 403 })
    }

    const client = await clientPromise
    const db = client.db()

    const startDate = new Date()
    switch (timeRange) {
      case "7d":
        startDate.setDate(startDate.getDate() - 7)
        break
      case "30d":
        startDate.setDate(startDate.getDate() - 30)
        break
      case "90d":
        startDate.setDate(startDate.getDate() - 90)
        break
      case "1y":
        startDate.setFullYear(startDate.getFullYear() - 1)
        break
      default:
        startDate.setFullYear(2000)
    }

    const businessNfts = await db
      .collection("nfts")
      .find({
        creatorId: new ObjectId(user.id),
        isActive: true,
      })
      .toArray()

    const nftIds = businessNfts.map((nft) => nft._id)

    const transfers = await db
      .collection("transfers")
      .find({
        nftId: { $in: nftIds },
        transferredAt: { $gte: startDate },
      })
      .toArray()

    const itemsSold = transfers.length

    const newCustomers = new Set<string>()
    transfers.forEach((transfer) => {
      if (transfer.recipientId && transfer.recipientId.toString() !== user.id) {
        newCustomers.add(transfer.recipientId.toString())
      }
    })

    const totalRevenue = transfers.length

    const daysDiff = Math.max(1, Math.ceil((new Date().getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)))
    const avgDailyTrades = transfers.length / daysDiff

    const salesByDay: Record<string, number> = {}
    const currentDate = new Date()

    for (let i = 0; i < daysDiff; i++) {
      const date = new Date()
      date.setDate(currentDate.getDate() - i)
      const dateString = date.toISOString().split("T")[0]
      salesByDay[dateString] = 0
    }

    transfers.forEach((transfer) => {
      const date = new Date(transfer.transferredAt).toISOString().split("T")[0]
      if (salesByDay[date] !== undefined) {
        salesByDay[date]++
      }
    })

    const salesOverTime = Object.entries(salesByDay)
      .map(([date, count]) => ({
        date,
        count,
      }))
      .reverse()

    const categoryDistribution: Record<string, number> = {}

    transfers.forEach((transfer) => {
      const nft = businessNfts.find((n) => n._id.toString() === transfer.nftId.toString())
      if (nft) {
        const category = nft.brand || "Unknown"
        categoryDistribution[category] = (categoryDistribution[category] || 0) + 1
      }
    })

    const distributionByCategory = Object.entries(categoryDistribution).map(([category, count]) => ({
      category,
      count,
    }))

    const itemSales: Record<string, number> = {}
    transfers.forEach((transfer) => {
      const nftId = transfer.nftId.toString()
      itemSales[nftId] = (itemSales[nftId] || 0) + 1
    })

    const topSellingItems = Object.entries(itemSales)
      .map(([nftId, sales]) => {
        const nft = businessNfts.find((n) => n._id.toString() === nftId)
        return {
          id: nftId,
          name: nft ? nft.name : "Unknown Item",
          sales,
          revenue: sales, 
        }
      })
      .sort((a, b) => b.sales - a.sales)
      .slice(0, 5)

    const customerPurchases: Record<string, number> = {}
    transfers.forEach((transfer) => {
      if (transfer.recipientId && transfer.recipientId.toString() !== user.id) {
        const customerId = transfer.recipientId.toString()
        customerPurchases[customerId] = (customerPurchases[customerId] || 0) + 1
      }
    })

    const topCustomerIds = Object.entries(customerPurchases)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([id]) => new ObjectId(id))

    const customerDetails = await db
      .collection("users")
      .find({ _id: { $in: topCustomerIds } })
      .toArray()

    const topCustomers = Object.entries(customerPurchases)
      .map(([customerId, purchases]) => {
        const customer = customerDetails.find((c) => c._id.toString() === customerId)
        return {
          id: customerId,
          name: customer ? customer.name : "Unknown Customer",
          purchases,
          spent: purchases,
        }
      })
      .sort((a, b) => b.purchases - a.purchases)
      .slice(0, 5)

    return NextResponse.json({
      success: true,
      analytics: {
        itemsSold,
        newCustomers: newCustomers.size,
        totalRevenue,
        avgDailyTrades,
        salesOverTime,
        distributionByCategory,
        topSellingItems,
        topCustomers,
      },
    })
  } catch (error) {
    console.error("Error fetching analytics:", error)
    return NextResponse.json({ success: false, message: "Failed to fetch analytics" }, { status: 500 })
  }
}
