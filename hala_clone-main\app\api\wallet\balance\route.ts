import { NextResponse } from "next/server"
import { Connection, PublicKey, LAMPORTS_PER_SOL } from "@solana/web3.js"

const RPC_ENDPOINT = "https://mainnet.helius-rpc.com/"

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const address = searchParams.get("address")

    if (!address) {
      return NextResponse.json({ error: "Wallet address is required" }, { status: 400 })
    }

    const connection = new Connection(RPC_ENDPOINT)

    try {
      const publicKey = new PublicKey(address)
      const balance = await connection.getBalance(publicKey)

      return NextResponse.json({
        balance: balance / LAMPORTS_PER_SOL,
      })
    } catch (error) {
      console.error("Error fetching balance:", error)
      return NextResponse.json({ error: "Invalid wallet address or RPC error" }, { status: 400 })
    }
  } catch (error) {
    console.error("Server error:", error)
    return NextResponse.json({ error: "Server error" }, { status: 500 })
  }
}
