-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create users table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  name VA<PERSON>HA<PERSON>(255) NOT NULL,
  role VARCHAR(20) <PERSON><PERSON><PERSON> (role IN ('customer', 'business')) NOT NULL DEFAULT 'customer',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  business_name VARCHAR(255),
  business_vat_number VARCHAR(100),
  is_verified BOOLEAN DEFAULT FALSE
);

-- Create items table (formerly nfts)
CREATE TABLE items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VA<PERSON>HA<PERSON>(255) NOT NULL,
  description TEXT,
  image_url TEXT NOT NULL,
  brand VARCHAR(255),
  year VARCHAR(10),
  serial_number VARCHAR(100),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  creator_id UUID REFERENCES users(id) ON DELETE CASCADE,
  owner_id UUID REFERENCES users(id) ON DELETE CASCADE,
  is_active BOOLEAN DEFAULT TRUE
);

-- Create ownership_history table
CREATE TABLE ownership_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  item_id UUID REFERENCES items(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  user_name VARCHAR(255) NOT NULL,
  transferred_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  transferred_from UUID REFERENCES users(id) ON DELETE SET NULL,
  transferred_from_name VARCHAR(255)
);

-- Create transfers table
CREATE TABLE transfers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  item_id UUID REFERENCES items(id) ON DELETE CASCADE,
  item_name VARCHAR(255) NOT NULL,
  sender_id UUID REFERENCES users(id) ON DELETE CASCADE,
  sender_name VARCHAR(255) NOT NULL,
  sender_email VARCHAR(255) NOT NULL,
  recipient_id UUID REFERENCES users(id) ON DELETE CASCADE,
  recipient_email VARCHAR(255) NOT NULL,
  message TEXT,
  transferred_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  status VARCHAR(50) DEFAULT 'pending'
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_items_creator_id ON items(creator_id);
CREATE INDEX idx_items_owner_id ON items(owner_id);
CREATE INDEX idx_items_is_active ON items(is_active);
CREATE INDEX idx_ownership_history_item_id ON ownership_history(item_id);
CREATE INDEX idx_ownership_history_user_id ON ownership_history(user_id);
CREATE INDEX idx_transfers_sender_id ON transfers(sender_id);
CREATE INDEX idx_transfers_recipient_id ON transfers(recipient_id);
CREATE INDEX idx_transfers_item_id ON transfers(item_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for users table
CREATE TRIGGER update_users_updated_at 
  BEFORE UPDATE ON users 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE items ENABLE ROW LEVEL SECURITY;
ALTER TABLE ownership_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE transfers ENABLE ROW LEVEL SECURITY;

-- Create RLS policies

-- Users can read their own data
CREATE POLICY "Users can read own data" ON users
  FOR SELECT USING (auth.uid()::text = id::text);

-- Users can update their own data
CREATE POLICY "Users can update own data" ON users
  FOR UPDATE USING (auth.uid()::text = id::text);

-- Items policies
CREATE POLICY "Users can read all items" ON items
  FOR SELECT USING (true);

-- Users can create items
CREATE POLICY "Users can create items" ON items
  FOR INSERT WITH CHECK (auth.uid()::text = creator_id::text);

-- Users can update items they own
CREATE POLICY "Users can update own items" ON items
  FOR UPDATE USING (auth.uid()::text = owner_id::text);

-- Ownership history policies
CREATE POLICY "Users can read ownership history" ON ownership_history
  FOR SELECT USING (true);

CREATE POLICY "System can insert ownership history" ON ownership_history
  FOR INSERT WITH CHECK (true);

-- Transfers policies
CREATE POLICY "Users can read transfers they're involved in" ON transfers
  FOR SELECT USING (
    auth.uid()::text = sender_id::text OR 
    auth.uid()::text = recipient_id::text
  );

CREATE POLICY "Users can create transfers for items they own" ON transfers
  FOR INSERT WITH CHECK (auth.uid()::text = sender_id::text);
