import { NextResponse } from "next/server"
import { cookies } from "next/headers"
import jwt from "jsonwebtoken"
import clientPromise from "@/lib/mongodb"
import { ObjectId } from "mongodb"
import type { UserSession } from "@/lib/models/user"

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key"

export async function GET() {
  try {
    const token = cookies().get("auth-token")?.value
    if (!token) {
      return NextResponse.json({ success: false, message: "Unauthorized" }, { status: 401 })
    }

    let user: UserSession
    try {
      user = jwt.verify(token, JWT_SECRET) as UserSession
    } catch (error) {
      return NextResponse.json({ success: false, message: "Invalid token" }, { status: 401 })
    }

    if (user.role !== "business") {
      return NextResponse.json({ success: false, message: "Unauthorized. Business role required" }, { status: 403 })
    }

    const client = await clientPromise
    const db = client.db()

    const tokenizedItems = await db.collection("nfts").countDocuments({
      creatorId: new ObjectId(user.id),
      isActive: true,
    })

    const businessNfts = await db
      .collection("nfts")
      .find({
        creatorId: new ObjectId(user.id),
        isActive: true,
      })
      .toArray()

    const nftIds = businessNfts.map((nft) => nft._id)

    const transfers = await db
      .collection("transfers")
      .find({
        nftId: { $in: nftIds },
      })
      .toArray()

    const customerIds = new Set<string>()
    transfers.forEach((transfer) => {
      if (transfer.recipientId && transfer.recipientId.toString() !== user.id) {
        customerIds.add(transfer.recipientId.toString())
      }
    })
    const activeCustomers = customerIds.size

    const totalValue = businessNfts.length

    const tradesPerNft: Record<string, number> = {}
    businessNfts.forEach((nft) => {
      const nftId = nft._id.toString()
      const nftTrades = transfers.filter((t) => t.nftId.toString() === nftId).length
      tradesPerNft[nftId] = nftTrades
    })

    const recentTrades = await db
      .collection("transfers")
      .find({ nftId: { $in: nftIds } })
      .sort({ transferredAt: -1 })
      .limit(5)
      .toArray()

    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const recentTransfers = await db
      .collection("transfers")
      .find({
        nftId: { $in: nftIds },
        transferredAt: { $gte: thirtyDaysAgo },
      })
      .toArray()

    const tradesByDay: Record<string, number> = {}
    for (let i = 0; i < 30; i++) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      const dateString = date.toISOString().split("T")[0]
      tradesByDay[dateString] = 0
    }

    recentTransfers.forEach((transfer) => {
      const date = new Date(transfer.transferredAt).toISOString().split("T")[0]
      if (tradesByDay[date] !== undefined) {
        tradesByDay[date]++
      }
    })

    const tradesOverTime = Object.entries(tradesByDay)
      .map(([date, count]) => ({
        date,
        count,
      }))
      .reverse()

    const collectionTradeCount: Record<string, { count: number; name: string }> = {}

    for (const nft of businessNfts) {
      const collectionId = nft.collectionId ? nft.collectionId.toString() : "uncategorized"
      const collectionName = nft.collectionName || nft.name || "Uncategorized"

      if (!collectionTradeCount[collectionId]) {
        collectionTradeCount[collectionId] = { count: 0, name: collectionName }
      }
    }

    for (const transfer of transfers) {
      const nft = businessNfts.find((n) => n._id.toString() === transfer.nftId.toString())
      if (nft) {
        const collectionId = nft.collectionId ? nft.collectionId.toString() : "uncategorized"
        collectionTradeCount[collectionId].count++
      }
    }

    const topCollections = Object.values(collectionTradeCount)
      .sort((a, b) => b.count - a.count)
      .slice(0, 5)
      .map((collection) => ({
        name: collection.name,
        count: collection.count,
        percentage: Math.round((collection.count / transfers.length) * 100) || 0,
      }))

    return NextResponse.json({
      success: true,
      stats: {
        tokenizedItems,
        activeCustomers,
        totalValue,
        tradesPerNft,
        recentTrades,
        tradesOverTime,
        topCollections,
      },
    })
  } catch (error) {
    console.error("Error fetching business stats:", error)
    return NextResponse.json({ success: false, message: "Failed to fetch business stats" }, { status: 500 })
  }
}
