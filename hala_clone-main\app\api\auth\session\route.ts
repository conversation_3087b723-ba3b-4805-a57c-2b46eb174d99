import { NextResponse } from "next/server";
import { cookies } from "next/headers";
import jwt from "jsonwebtoken";
import type { UserSession } from "@/lib/models/user";

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key";

export async function GET() {
  try {
    const token = (await cookies()).get("auth-token")?.value;

    if (!token) {
      return NextResponse.json({ user: null });
    }

    try {
      const user = jwt.verify(token, JWT_SECRET) as UserSession;
      return NextResponse.json({ user });
    } catch (error) {
      (await cookies()).set({
        name: "auth-token",
        value: "",
        httpOnly: true,
        path: "/",
        secure: process.env.NODE_ENV === "production",
        maxAge: 0,
      });
      return NextResponse.json({ user: null });
    }
  } catch (error) {
    console.error("Session error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
