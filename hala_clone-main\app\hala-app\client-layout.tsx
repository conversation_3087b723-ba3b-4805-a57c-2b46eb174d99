"use client";

import type React from "react";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth-context";
import { Home, Package, LayoutDashboard, User, LogOut, Menu, X, BarChart2, FileText, ShoppingBag } from "lucide-react";
import NavBarBottom from "@/components/custom/navBarBottom";
import Footer from "@/components/custom/footer";

export function ClientLayout({ children }: { children: React.ReactNode; }) {
  const { user, logout } = useAuth();
  const pathname = usePathname();
  const router = useRouter();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isBusinessMenuOpen, setIsBusinessMenuOpen] = useState(false);

  useEffect(() => {
    if (!user) {
      router.push("/auth/login");
    }
  }, [user, router]);

  const handleLogout = async () => {
    try {
      await logout();
      router.push("/auth/login");
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  if (!user) {
    return null;
  }

  const isBusinessUser = user.role === "business";

  const navItems = isBusinessUser
    ? []
    : [
      {
        name: "Dashboard",
        href: "/hala-app/dashboard",
        icon: <Home className="h-5 w-5" />,
      },
      {
        name: "My Items",
        href: "/hala-app/wallet",
        icon: <Package className="h-5 w-5" />,
      },
      {
        name: "KYC",
        href: "/hala-app/kyc",
        icon: <User className="h-5 w-5" />,
      },

      {
        name: "Shop",
        href: "/hala-app/shop",
        icon: <User className="h-5 w-5" />,
      },
    ];

  const businessNavItems = [
    {
      name: "Business Dashboard",
      href: "/hala-app/business/dashboard",
      icon: <LayoutDashboard className="h-5 w-5" />,
    },
    {
      name: "Business Minting",
      href: "/hala-app/business/minting",
      icon: <ShoppingBag className="h-5 w-5" />,
    },
    {
      name: "Collections",
      href: "/hala-app/business/collections",
      icon: <Package className="h-5 w-5" />,
    },
    {
      name: "Analytics",
      href: "/hala-app/business/analytics",
      icon: <BarChart2 className="h-5 w-5" />,
    },
    {
      name: "KYB",
      href: "/hala-app/business/kyb",
      icon: <FileText className="h-5 w-5" />,
    },
  ];

  return (
    <div className="bg-gray-50">
      <div className="flex min-h-[1000px]">
        <div className="hidden h-3/5 md:flex md:w-64 md:flex-col md:fixed md:inset-y-0 p-4 ">
          <div className="flex flex-col flex-grow bg-white border border-gray-200 rounded-md">
            <div className="flex items-center justify-center h-16 px-4 border-b border-gray-200">
              <Link
                href={isBusinessUser ? "/hala-app/business/dashboard" : "/hala-app/dashboard"}
                className="flex items-center"
              >
                <span className="text-xl font-bold">HALA</span>
              </Link>
            </div>
            <div className="flex flex-col flex-grow overflow-y-auto">
              <nav className="flex-1 px-4 py-4 space-y-1">
                {navItems.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`flex items-center px-4 py-2 text-sm font-medium rounded-md ${pathname === item.href
                      ? "bg-gray-100 text-gray-900"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                      }`}
                  >
                    {item.icon}
                    <span className="ml-3">{item.name}</span>
                  </Link>
                ))}

                {isBusinessUser
                  ?
                  businessNavItems.map((item) => (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={`flex items-center px-4 py-2 text-sm font-medium rounded-md ${pathname === item.href
                        ? "bg-gray-100 text-gray-900"
                        : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                        }`}
                    >
                      {item.icon}
                      <span className="ml-3">{item.name}</span>
                    </Link>
                  ))
                  : null}
              </nav>
              <div className="flex-shrink-0 p-4 border-t border-gray-200">
                <button
                  onClick={handleLogout}
                  className="flex items-center w-full px-4 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-50 hover:text-gray-900"
                >
                  <LogOut className="h-5 w-5" />
                  <span className="ml-3">Logout</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="md:hidden">
          {isMobileMenuOpen && (
            <div className="fixed inset-0 z-40 flex">
              <div
                className="fixed inset-0 bg-gray-600 bg-opacity-75 transition-opacity ease-in-out duration-300"
                onClick={() => setIsMobileMenuOpen(false)}
              ></div>

              <div className="relative flex-1 flex flex-col max-w-xs w-full bg-white transform transition ease-in-out duration-300">
                <div className="absolute top-0 right-0 -mr-12 pt-2">
                  <button
                    className="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <span className="sr-only">Close sidebar</span>
                    <X className="h-6 w-6 text-white" />
                  </button>
                </div>

                <div className="flex-1 h-0 pt-5 pb-4 overflow-y-auto">
                  <div className="flex-shrink-0 flex items-center px-4">
                    <span className="text-xl font-bold">HALA</span>
                  </div>
                  <nav className="mt-5 px-2 space-y-1">
                    {navItems.map((item) => (
                      <Link
                        key={item.name}
                        href={item.href}
                        className={`flex items-center px-4 py-2 text-base font-medium rounded-md ${pathname === item.href
                          ? "bg-gray-100 text-gray-900"
                          : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                          }`}
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        {item.icon}
                        <span className="ml-3">{item.name}</span>
                      </Link>
                    ))}

                    {isBusinessUser &&
                      businessNavItems.map((item) => (
                        <Link
                          key={item.name}
                          href={item.href}
                          className={`flex items-center px-4 py-2 text-base font-medium rounded-md ${pathname === item.href
                            ? "bg-gray-100 text-gray-900"
                            : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                            }`}
                          onClick={() => setIsMobileMenuOpen(false)}
                        >
                          {item.icon}
                          <span className="ml-3">{item.name}</span>
                        </Link>
                      ))}
                  </nav>
                </div>
                <div className="flex-shrink-0 p-4 border-t border-gray-200">
                  <button
                    onClick={handleLogout}
                    className="flex items-center w-full px-4 py-2 text-base font-medium text-gray-600 rounded-md hover:bg-gray-50 hover:text-gray-900"
                  >
                    <LogOut className="h-5 w-5" />
                    <span className="ml-3">Logout</span>
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="md:pl-64 flex flex-col flex-1">
          <div className="sticky top-0 z-10 md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-white border-b border-gray-200">
            <button
              type="button"
              className="-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500"
              onClick={() => setIsMobileMenuOpen(true)}
            >
              <span className="sr-only">Open sidebar</span>
              <Menu className="h-6 w-6" />
            </button>
          </div>
          <main className="flex-1">
            <div className="py-6">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">{children}</div>
            </div>
          </main>
        </div>


      </div>
      <NavBarBottom />
      <Footer />
    </div>
  );
}

export default ClientLayout;
