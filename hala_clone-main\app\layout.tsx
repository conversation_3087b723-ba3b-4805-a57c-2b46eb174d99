import type React from "react";
import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

import { AuthProvider } from "@/lib/auth-context";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "HALA",
  description:
    "HALA is a FinTech platform for managing and tracking physical goods as digital assets. Secure, traceable, and monetizable.",
  keywords: "digital assets, physical assets, tracking, management, FinTech, HALA",
  authors: [{ name: "HALA Team" }],
  creator: "HALA",
  publisher: "HALA",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https:www.hala.wtf/",
    title: "HALA - Transform the past into future value",
    description: "Manage and track physical goods as digital assets",
    siteName: "HALA",
    images: [
      {
        url: "https://www.hala.wtf/og-image.png",
        width: 1200,
        height: 630,
        alt: "HALA - Digital Asset Management Platform",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "HALA - Transform the past into future value",
    description: "Manage and track physical goods as digital assets",
    images: ["https://www.hala.wtf/og-image.png"],
  },
  icons: {
    icon: "https://www.hala.wtf/icon2.png",
    apple: "https://www.hala.wtf/icon2.png",
    shortcut: "https://www.hala.wtf/icon2.png",
  },
  generator: 'v0.dev'
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="https://www.hala.wtf/icon2.png" />
      </head>
      <body className={inter.className}>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
