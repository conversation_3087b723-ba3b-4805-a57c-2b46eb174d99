import type React from "react";
import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { WalletContextProvider } from "@/components/wallet/wallet-provider";
import { AuthProvider } from "@/lib/auth-context";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "HALA",
  description:
    "HALA is a FinTech platform that integrates blockchain to tokenize physical goods, transforming them into unique digital assets through NFTs. Secure, traceable, and monetizable.",
  keywords: "blockchain, tokenization, NFT, physical assets, digital assets, FinTech, HALA",
  authors: [{ name: "HALA Team" }],
  creator: "HALA",
  publisher: "HALA",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https:www.hala.wtf/",
    title: "HALA - Transform the past into future value",
    description: "Tokenize physical goods into unique digital assets through NFTs",
    siteName: "HALA",
    images: [
      {
        url: "https://www.hala.wtf/og-image.png",
        width: 1200,
        height: 630,
        alt: "HALA - Blockchain Tokenization Platform",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "HALA - Transform the past into future value",
    description: "Tokenize physical goods into unique digital assets through NFTs",
    images: ["https://www.hala.wtf/og-image.png"],
  },
  icons: {
    icon: "https://www.hala.wtf/icon2.png",
    apple: "https://www.hala.wtf/icon2.png",
    shortcut: "https://www.hala.wtf/icon2.png",
  },
  generator: 'v0.dev'
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="https://www.hala.wtf/icon2.png" />
      </head>
      <body className={inter.className}>
        <AuthProvider>
          <WalletContextProvider>{children}</WalletContextProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
