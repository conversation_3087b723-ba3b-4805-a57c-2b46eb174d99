export type UserRole = "customer" | "business"

export interface User {
  id?: string
  email: string
  password: string
  name: string
  role: UserRole
  created_at: string
  updated_at: string
  business_name?: string
  business_vat_number?: string
  is_verified?: boolean
}

export interface UserSession {
  id: string
  email: string
  name: string
  role: User<PERSON><PERSON>
  business_name?: string
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface SignupData {
  email: string
  password: string
  name: string
  role: UserRole
  business_name?: string
  business_vat_number?: string
}
