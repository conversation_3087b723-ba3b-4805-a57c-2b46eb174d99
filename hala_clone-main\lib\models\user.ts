import type { ObjectId } from "mongodb"

export type UserRole = "customer" | "business"

export interface User {
  _id?: ObjectId
  email: string
  password: string
  name: string
  role: UserRole
  createdAt: Date
  updatedAt: Date
  businessName?: string
  businessVatNumber?: string
  isVerified?: boolean
}

export interface UserSession {
  id: string
  email: string
  name: string
  role: UserRole
  businessName?: string
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface SignupData {
  email: string
  password: string
  name: string
  role: UserRole
  businessName?: string
  businessVatNumber?: string
}
