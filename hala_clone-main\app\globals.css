@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;

    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;

    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;

    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;

    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;

    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;

    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    scroll-behavior: smooth;
  }

  html {
    scroll-behavior: smooth;
  }
}

.parallax-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  will-change: transform;
}

/* Smooth transitions for parallax elements */
.parallax-element {
  transition: transform 0.1s cubic-bezier(0.33, 1, 0.68, 1);
  will-change: transform;
}

/* Extra small text size for mobile */
.text-xxs {
  font-size: 0.65rem;
  line-height: 1rem;
}

/* Wallet adapter button styling */
.wallet-adapter-button {
  background: linear-gradient(to right, #1a1a1a, #000000) !important;
  color: white !important;
  border-radius: 9999px !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  padding: 0.5rem 1rem !important;
  border: none !important;
  cursor: pointer !important;
  transition: all 0.3s !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.wallet-adapter-button:hover {
  background: linear-gradient(to right, #000000, #1a1a1a) !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

.wallet-adapter-button-trigger {
  background: linear-gradient(to right, #1a1a1a, #000000) !important;
}

.wallet-adapter-dropdown-list {
  border-radius: 1rem !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

.wallet-adapter-dropdown-list-item {
  border-radius: 0.5rem !important;
  margin: 0.2rem !important;
  transition: all 0.2s !important;
}

.wallet-adapter-dropdown-list-item:hover {
  background-color: #f3f4f6 !important;
}

/* Custom button styles */
.hala-button {
  @apply bg-gradient-to-r from-gray-900 to-black hover:from-black hover:to-gray-800 text-white rounded-full text-sm font-medium uppercase tracking-wider transition-all duration-300 shadow-md;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .wallet-adapter-button {
    font-size: 0.75rem !important;
    padding: 0.5rem 0.75rem !important;
  }
}
