"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { PageTransition } from "@/components/page-transition";
import { Card } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Loader2 } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import CustomCard from "@/components/custom/card";

interface Item {
  _id: string;
  name: string;
  imageUrl: string;
  description: string;
  brand?: string;
  serialNumber?: string;
  createdAt: string;
}

export default function ItemsPage() {
  const [items, setItems] = useState<Item[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    fetchMyItems();
  }, []);

  const fetchMyItems = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/nft/my-nfts");
      const data = await response.json();

      if (data.success) {
        setItems(data.nfts);
      } else {
        throw new Error(data.message || "Failed to fetch items");
      }
    } catch (error) {
      console.error("Error fetching items:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to fetch items",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <PageTransition>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl md:text-3xl font-light">My Items</h1>
          <p className="text-gray-500 mt-2 text-sm md:text-base">View and manage your digital items</p>
        </div>

        <Tabs defaultValue="items" className="space-y-6">
          <TabsList>
            <TabsTrigger value="items">My Items</TabsTrigger>
            <TabsTrigger value="activity">Activity</TabsTrigger>
          </TabsList>

          <TabsContent value="items">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {isLoading ? (
                <div className="col-span-full flex justify-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
                </div>
              ) : items.length === 0 ? (
                <div className="col-span-full text-center py-12">
                  <p className="text-gray-500 mb-4">You don't have any items yet</p>
                  <Link
                    href="/hala-app/shop"
                    className="text-sm text-black underline hover:text-gray-700 transition-colors"
                  >
                    Browse available items
                  </Link>
                </div>
              ) : (
                items.map((item) => (
                  <CustomCard
                    key={item._id}
                    {...item}
                    brand={item.brand ?? ""}
                  />
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="activity">
            <Card className="p-6">
              <div className="text-center py-8">
                <p className="text-gray-500">Activity history will be available soon</p>
              </div>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </PageTransition>
  );
}
