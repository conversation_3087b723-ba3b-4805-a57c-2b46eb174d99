import { NextResponse } from "next/server"
import { cookies } from "next/headers"
import jwt from "jsonwebtoken"
import clientPromise from "@/lib/mongodb"
import { ObjectId } from "mongodb"
import type { UserSession } from "@/lib/models/user"

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key"

export async function GET() {
  try {
    const token = cookies().get("auth-token")?.value
    if (!token) {
      return NextResponse.json({ success: false, message: "Unauthorized" }, { status: 401 })
    }

    let user: UserSession
    try {
      user = jwt.verify(token, JWT_SECRET) as UserSession
    } catch (error) {
      return NextResponse.json({ success: false, message: "Invalid token" }, { status: 401 })
    }

    const client = await clientPromise
    const db = client.db()

    const transfers = await db
      .collection("transfers")
      .find({ senderId: new ObjectId(user.id) })
      .sort({ transferredAt: -1 })
      .toArray()

    return NextResponse.json({
      success: true,
      transfers,
    })
  } catch (error) {
    console.error("Error fetching transfer history:", error)
    return NextResponse.json({ success: false, message: "Failed to fetch transfer history" }, { status: 500 })
  }
}
