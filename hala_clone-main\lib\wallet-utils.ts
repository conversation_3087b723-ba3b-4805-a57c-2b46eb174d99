import Cookies from "js-cookie"
import { Connection, PublicKey, LAMPORTS_PER_SOL } from "@solana/web3.js"

export const getWalletAddress = (): string | null => {
  return Cookies.get("walletAddress") || null
}

export const isWalletConnected = (): boolean => {
  return !!getWalletAddress()
}

export const getSolBalance = async (address: string): Promise<number | null> => {
  try {
    const response = await fetch(`/api/wallet/balance?address=${address}`)

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`)
    }

    const data = await response.json()

    if (data.balance !== undefined) {
      return data.balance
    }

    return getBalanceDirect(address)
  } catch (error) {
    console.error("Error fetching balance from API:", error)
    return getBalanceDirect(address)
  }
}

const getBalanceDirect = async (address: string): Promise<number | null> => {
  try {
    const connection = new Connection("https://api.mainnet-beta.solana.com", "confirmed")
    const publicKey = new PublicKey(address)
    const balance = await connection.getBalance(publicKey)
    return balance / LAMPORTS_PER_SOL
  } catch (error) {
    console.error("Error fetching balance directly:", error)
    return null
  }
}
