import { NextResponse } from "next/server"
import { cookies } from "next/headers"
import jwt from "jsonwebtoken"
import { itemService, ownershipService, convertFromNFTFormat } from "@/lib/database"
import type { NFTCreateInput } from "@/lib/models/nft"
import type { UserSession } from "@/lib/models/user"

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key"

export async function POST(request: Request) {
  try {
    const token = cookies().get("auth-token")?.value
    if (!token) {
      return NextResponse.json({ success: false, message: "Unauthorized" }, { status: 401 })
    }

    let user: UserSession
    try {
      user = jwt.verify(token, JWT_SECRET) as UserSession
    } catch (error) {
      return NextResponse.json({ success: false, message: "Invalid token" }, { status: 401 })
    }

    if (user.role !== "business") {
      return NextResponse.json({ success: false, message: "Only business users can mint NFTs" }, { status: 403 })
    }

    const body = await request.json()
    const { name, description, imageUrl, brand, year, serialNumber } = body as NFTCreateInput

    if (!name || !imageUrl) {
      return NextResponse.json({ success: false, message: "Name and image URL are required" }, { status: 400 })
    }

    // Convert from NFT format to Item format
    const itemData = {
      name,
      description,
      image_url: imageUrl,
      brand,
      year,
      serial_number: serialNumber,
      creator_id: user.id,
      owner_id: user.id,
      is_active: true,
    }

    const newItem = await itemService.create(itemData)

    // Add initial ownership record
    await ownershipService.addRecord({
      item_id: newItem.id!,
      user_id: user.id,
      user_name: user.name,
    })

    return NextResponse.json({
      success: true,
      message: "Item created successfully",
      itemId: newItem.id,
    })
  } catch (error) {
    console.error("Error creating NFT:", error)
    return NextResponse.json({ success: false, message: "Failed to create item" }, { status: 500 })
  }
}
