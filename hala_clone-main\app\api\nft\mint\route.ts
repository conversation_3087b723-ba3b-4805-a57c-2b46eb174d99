import { NextResponse } from "next/server"
import { cookies } from "next/headers"
import jwt from "jsonwebtoken"
import clientPromise from "@/lib/mongodb"
import { ObjectId } from "mongodb"
import type { NFTCreateInput } from "@/lib/models/nft"
import type { UserSession } from "@/lib/models/user"

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key"

export async function POST(request: Request) {
  try {
    const token = cookies().get("auth-token")?.value
    if (!token) {
      return NextResponse.json({ success: false, message: "Unauthorized" }, { status: 401 })
    }

    let user: UserSession
    try {
      user = jwt.verify(token, JWT_SECRET) as UserSession
    } catch (error) {
      return NextResponse.json({ success: false, message: "Invalid token" }, { status: 401 })
    }

    if (user.role !== "business") {
      return NextResponse.json({ success: false, message: "Only business users can mint NFTs" }, { status: 403 })
    }

    const body = await request.json()
    const { name, description, imageUrl, brand, year, serialNumber } = body as NFTCreateInput

    if (!name || !imageUrl) {
      return NextResponse.json({ success: false, message: "Name and image URL are required" }, { status: 400 })
    }

    const client = await clientPromise
    const db = client.db()

    const nft = {
      name,
      description,
      imageUrl,
      brand,
      year,
      serialNumber,
      createdAt: new Date(),
      creatorId: new ObjectId(user.id),
      ownerId: new ObjectId(user.id),
      ownershipHistory: [
        {
          userId: new ObjectId(user.id),
          userName: user.name,
          transferredAt: new Date(),
        },
      ],
      isActive: true,
    }

    const result = await db.collection("nfts").insertOne(nft)

    return NextResponse.json({
      success: true,
      message: "NFT created successfully",
      nftId: result.insertedId,
    })
  } catch (error) {
    console.error("Error creating NFT:", error)
    return NextResponse.json({ success: false, message: "Failed to create NFT" }, { status: 500 })
  }
}
