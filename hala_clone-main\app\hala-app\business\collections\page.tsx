"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";

import { Input } from "@/components/ui/input";
import { AnimatePresence, motion } from "framer-motion";
import { Search, Tag, Loader2 } from "lucide-react";
import { Card } from "@/components/ui/card";
import CustomCard from "@/components/custom/card";

import Image from "next/image";

import { PageTransition } from "@/components/page-transition";
import { useToast } from "@/components/ui/use-toast";
import Pagination from "@/components/custom/pagination";

import ok from "@/public/icons/ok.svg";
interface NFT {
  _id: string;
  name: string;
  description?: string;
  imageUrl: string;
  brand?: string;
  year?: string;
  serialNumber?: string;
  createdAt: string;
}

export default function BusinessCollections() {
  const [nfts, setNfts] = useState<NFT[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(true);

  const [selectedButton, setSelectedButton] = useState<string>('new');
  const [currentPage, setCurrentPage] = useState(1);
  const ITEMS_PER_PAGE = 6;

  const { toast } = useToast();

  useEffect(() => {
    fetchMyNfts();
  }, []);

  const fetchMyNfts = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/nft/my-nfts");
      const data = await response.json();

      if (data.success) {
        setNfts(data.nfts);
      } else {
        throw new Error(data.message || "Failed to fetch NFTs");
      }
    } catch (error) {
      console.error("Error fetching NFTs:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to fetch NFTs",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const filteredNFTs = nfts.filter((nft) =>
    nft.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    nft.brand?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  console.log("Filtered NFTs:", filteredNFTs);

  const sortedNFTs = [...filteredNFTs].sort((a, b) => {
    switch (selectedButton) {
      case "new":
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      default:
        return 0;
    }
  });

  const totalPages = Math.ceil(nfts.length / ITEMS_PER_PAGE);
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
  const endIndex = startIndex + ITEMS_PER_PAGE;
  const currentNFTs = sortedNFTs.slice(startIndex, endIndex);

  const buttons = [
    { id: "new", label: "New" },
    { id: "price-asc", label: "Price ascending" },
    { id: "price-desc", label: "Price descending" },
    { id: "rating", label: "Rating" },
  ];
  return (
    <div className="flex flex-col" >
      <div className="flex flex-col lg:flex-row items-center justify-center gap-4">
        <div className="w-full md:max-w-[300px] lg:max-w-[600px] relative flex-grow rounded-lg">
          <Input
            type="text"
            placeholder="Search"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="rounded-3xl w-full pl-10 pr-4 py-2 border border-gray-200 focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
          />
          <Search className="absolute left-[90%] top-1/2 transform -translate-y-1/2 text-gray-400 " size={18} />
        </div>
        <div className="flex items-center gap-2 flex-wrap justify-center">
          {buttons.map((btn) => (
            <Button
              key={btn.id}
              variant={selectedButton === btn.id ? "default" : "secondary"}
              className="gap-2"
              onClick={() => setSelectedButton(btn.id)}
            >
              {selectedButton === btn.id && <Image src={ok} alt="" />}
              {btn.label}
            </Button>
          ))}
        </div>
      </div>

      <PageTransition>
        <div className="space-y-6 min-h-[758px]">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="flex flex-col md:flex-row gap-4"
          >
          </motion.div>

          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
            </div>
          ) : currentNFTs.length === 0 ? (
            <Card className="p-8 text-center">
              <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-4">
                <Tag className="h-6 w-6 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium mb-2">No tokens found</h3>
              <p className="text-gray-500 mb-6">There are no tokens that match your search criteria.</p>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 place-items-center">
              <AnimatePresence mode="wait">
                {currentNFTs.map((nft, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.3 }}
                  >
                    <CustomCard
                      {...nft}
                      _id={index}
                      brand={nft.brand ?? ""}
                      link={"collections/"}
                    />
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          )}
        </div>
      </PageTransition>
      <div className="mt-4 flex gap-2 justify-center md:self-end">
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={(page) => setCurrentPage(page)}
        />
      </div>
    </div >
  );
}
