import { NextResponse } from "next/server";
import { cookies } from "next/headers";
import jwt from "jsonwebtoken";
import clientPromise from "@/lib/mongodb";
import { ObjectId } from "mongodb";
import type { UserSession } from "@/lib/models/user";

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key";

export async function GET(
  request: Request,
  context: { params: { id: string } }
) {
  try {
    const token = cookies().get("auth-token")?.value;
    if (!token) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    let user: UserSession;
    try {
      user = jwt.verify(token, JWT_SECRET) as UserSession;
    } catch (error) {
      return NextResponse.json(
        { success: false, message: "Invalid token" },
        { status: 401 }
      );
    }

    const { id } = context.params;

    const client = await clientPromise;
    const db = client.db();

    const nft = await db.collection("nfts").findOne({ _id: new ObjectId(id) });

    if (!nft) {
      return NextResponse.json(
        { success: false, message: "NFT not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      nft,
    });
  } catch (error) {
    console.error("Error fetching NFT details:", error);
    return NextResponse.json(
      { success: false, message: "Failed to fetch NFT details" },
      { status: 500 }
    );
  }
}
