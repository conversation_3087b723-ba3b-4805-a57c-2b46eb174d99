import type { ObjectId } from "mongodb"

export interface NFT {
  _id?: ObjectId
  name: string
  description?: string
  imageUrl: string
  brand?: string
  year?: string
  serialNumber?: string
  createdAt: Date
  creatorId: string | ObjectId
  ownerId: string | ObjectId
  ownershipHistory: OwnershipRecord[]
  isActive: boolean
}

export interface OwnershipRecord {
  userId: string | ObjectId
  userName: string
  transferredAt: Date
  transferredFrom?: string | ObjectId
  transferredFromName?: string
}

export interface NFTTransfer {
  nftId: string
  recipientEmail: string
  message?: string
}

export interface NFTCreateInput {
  name: string
  description?: string
  imageUrl: string
  brand?: string
  year?: string
  serialNumber?: string
}
