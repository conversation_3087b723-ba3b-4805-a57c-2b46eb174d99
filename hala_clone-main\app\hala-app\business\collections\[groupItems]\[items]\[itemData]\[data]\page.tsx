'use client';
import React, { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { notFound } from 'next/navigation';
import { toast } from "@/components/ui/use-toast";
import { useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';
import Traceability from '@/components/custom/traceability';
import PriceTrend from '@/components/custom/priceTrend';
import CommercialPerformance from '@/components/custom/commercialPerformace';

type Nft = {
  description: string;
  name: string;
  _id: string;
  brand: string;
  imageUrl: string;
};

export default function Data() {
  const [nfts, setNfts] = React.useState<Nft[]>([]);
  const [isLoading, setIsLoading] = React.useState(true);
  const router = useRouter();

  const pathname = usePathname();
  const pathSegments = pathname.split("/").filter((param) => param !== "");
  const route = pathSegments[pathSegments.length - 2];
  const last = pathSegments[pathSegments.length - 1].split("-")[0];



  useEffect(() => {
    fetchMyNfts();
  }, []);

  const fetchMyNfts = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/nft/my-nfts");
      const data = await response.json();
      console.log("Fetched NFTs:", data);

      if (!data.success) {
        throw new Error(data.message || "Failed to fetch NFTs");
      }

      const item = data.nfts.find((item: Nft) => item._id === last);
      if (!item) {
        notFound();
      }
      setNfts(data.nfts);
    } catch (error) {
      console.error("Error fetching NFTs:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to fetch NFTs",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const renderContent = () => {
    const nft = nfts[0];
    if (!nfts[0]) return <p>Carregando...</p>;
    switch (route) {
      case "traceability":
        return <Traceability {...nft} />;
      case "commercial-performance":
        return <CommercialPerformance />;
      case "price-trend":
        return <PriceTrend />;
      case "latest-trades":
        return <h2 className="font-bold">Latest Trades Route</h2>;
      default:
        return <h2 className="font-bold">Rota não encontrada</h2>;
    }
  };

  return (
    isLoading ? (
      <div className='flex items-center justify-center min-h-[758px]'>
        <Loader2 className='animate-spin' />
      </div>
    ) : (
      nfts.length === 0 ? (
        <div className="flex-1 flex flex-col min-h-[758px] justify-center items-center">
          <h1 className="text-2xl font-bold">No NFTs Found</h1>
          <button
            onClick={() => {
              if (window.history.length > 1) {
                router.back();
              } else {
                router.push('/');
              }
            }}
            className="text-blue-500 hover:underline"
          >
            Return to previous page
          </button>
        </div>
      ) : (
        <div className="flex justify-center items-center min-h-[758px]">
          {renderContent()}
        </div>
      )
    )
  );
}
