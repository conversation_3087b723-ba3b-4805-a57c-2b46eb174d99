import { NextResponse } from "next/server";
import clientPromise from "@/lib/mongodb";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import { cookies } from "next/headers";
import type { User, UserSession } from "@/lib/models/user";
import { ObjectId } from "mongodb";

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key";

export async function POST(request: Request) {
  try {
    const { email, password } = await request.json();

    if (!email || !password) {
      return NextResponse.json(
        { error: "Email and password are required" },
        { status: 400 }
      );
    }

    //Simulando usuario
    const user: User = {
      _id: new ObjectId("000000000000000000000001"), // só um exemplo
      email: email,
      name: "Teste Usuário",
      role: "business",
      businessName: "Teste Empresa",
      password: "$2a$10$XXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const isPasswordValid = true;

    /* 
      const client = await clientPromise;
      const db = client.db();
      const usersCollection = db.collection("users");
      const user = (await usersCollection.findOne({ email })) as User | null;
      const isPasswordValid = await bcrypt.compare(password, user.password)
    */

    if (!user) {
      return NextResponse.json(
        { error: "Invalid credentials" },
        { status: 401 }
      );
    }

    if (!isPasswordValid) {
      return NextResponse.json(
        { error: "Invalid credentials" },
        { status: 401 }
      );
    }

    const session: UserSession = {
      id: user._id!.toString(),
      email: user.email,
      name: user.name,
      role: user.role,
      businessName: user.businessName,
    };

    const token = jwt.sign(session, JWT_SECRET, { expiresIn: "7d" });

    (await cookies()).set({
      name: "auth-token",
      value: token,
      httpOnly: true,
      path: "/",
      secure: process.env.NODE_ENV === "production",
      maxAge: 60 * 60 * 24 * 7, // 1 week
    });

    return NextResponse.json({
      user: session,
      success: true,
      message: "Login successful",
    });
  } catch (error) {
    console.error("Login error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
